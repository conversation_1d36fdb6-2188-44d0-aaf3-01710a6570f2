// 导航组件加载器
(function () {
	// 获取正确的基础路径
	function getBasePath() {
		// 所有页面都在根目录，统一使用相对路径
		return "./";
	}

	// 加载导航组件
	function loadNavigation() {
		const navigationContainer = document.getElementById("navigation-container");
		if (!navigationContainer) return;

		// 检查是否有预加载的内容
		if (window.navigationHTML) {
			// 使用预加载的内容，几乎无延迟
			navigationContainer.innerHTML = window.navigationHTML;
			initNavigationInteractions();
			return;
		}

		// 异步加载导航内容
		const basePath = getBasePath();
		fetch(basePath + "component/navigation/navigation.html")
			.then((response) => response.text())
			.then((html) => {
				// 修复HTML中的路径
				html = fixNavigationPaths(html, basePath);
				navigationContainer.innerHTML = html;
				// 导航加载完成后初始化交互功能
				initNavigationInteractions();
			})
			.catch((error) => {
				console.error("导航组件加载失败:", error);
				navigationContainer.innerHTML =
					'<div class="navigation-error">导航加载失败</div>';
			});
	}

	// 修复导航HTML中的路径
	function fixNavigationPaths(html, basePath) {
		// 修复所有相对路径
		html = html.replace(/href="\.\/([^"]+)"/g, `href="${basePath}$1"`);
		html = html.replace(
			/onclick="window\.location\.href='\.\/([^']+)'/g,
			`onclick="window.location.href='${basePath}$1'`
		);
		return html;
	}

	// 初始化导航交互功能
	function initNavigationInteractions() {
		// 高亮当前页面对应的导航链接
		highlightCurrentPage();
	}

	// 高亮当前页面对应的导航链接
	function highlightCurrentPage() {
		const currentPath = window.location.pathname;
		const navLinks = document.querySelectorAll(".nav-link");

		navLinks.forEach((link) => {
			const linkPath = link.getAttribute("href");
			if (
				currentPath.includes(linkPath.replace("./", "")) ||
				(currentPath.includes("home") && linkPath.includes("home")) ||
				(currentPath.includes("aboutus") && linkPath.includes("aboutus")) ||
				(currentPath.includes("recruitment") &&
					linkPath.includes("recruitment")) ||
				(currentPath.includes("contact") && linkPath.includes("contact")) ||
				(currentPath.includes("preview") && linkPath.includes("preview")) ||
				(currentPath.includes("detail") && linkPath.includes("detail"))
			) {
				link.classList.add("active");
			}
		});
	}

	// 优化加载策略
	function initNavigation() {
		// 如果DOM已经准备好，立即加载
		if (document.readyState === "loading") {
			document.addEventListener("DOMContentLoaded", loadNavigation);
		} else {
			// DOM已经加载完成，立即执行
			loadNavigation();
		}
	}

	// 预加载导航内容（可选优化）
	function preloadNavigation() {
		const basePath = getBasePath();
		fetch(basePath + "component/navigation/navigation.html")
			.then((response) => response.text())
			.then((html) => {
				// 将预加载的内容存储在内存中
				window.navigationHTML = fixNavigationPaths(html, basePath);
			})
			.catch((error) => {
				console.warn("导航预加载失败:", error);
			});
	}

	// 如果支持，启动预加载
	if (document.readyState === "loading") {
		preloadNavigation();
	}

	// 初始化导航
	initNavigation();
})();
