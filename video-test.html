<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频播放测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-test {
            text-align: center;
            margin: 20px 0;
        }
        .test-video {
            width: 100%;
            max-width: 600px;
            height: auto;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>视频播放功能测试</h1>
        
        <div class="video-test">
            <h2>测试视频地址</h2>
            <p>视频URL: <code>https://stream7.iqilu.com/10339/upload_transcode/202002/09/20200209104902N3v5Vpxuvb.mp4</code></p>
            
            <video id="testVideo" class="test-video" controls preload="metadata">
                <source src="https://stream7.iqilu.com/10339/upload_transcode/202002/09/20200209104902N3v5Vpxuvb.mp4" type="video/mp4">
                您的浏览器不支持视频播放。
            </video>
            
            <div>
                <button class="test-button" onclick="testVideoLoad()">测试视频加载</button>
                <button class="test-button" onclick="testVideoPlay()">测试播放</button>
                <button class="test-button" onclick="testVideoPause()">测试暂停</button>
                <button class="test-button" onclick="clearStatus()">清除状态</button>
            </div>
            
            <div id="status"></div>
        </div>
        
        <div class="video-test">
            <h2>测试结果说明</h2>
            <ul>
                <li><strong>视频加载测试</strong>: 检查视频是否能正常加载元数据</li>
                <li><strong>播放测试</strong>: 检查视频是否能正常播放</li>
                <li><strong>暂停测试</strong>: 检查视频是否能正常暂停</li>
                <li><strong>网络检查</strong>: 如果视频无法加载，可能是网络问题或视频源问题</li>
            </ul>
        </div>
    </div>

    <script>
        const video = document.getElementById('testVideo');
        const statusDiv = document.getElementById('status');

        function addStatus(message, type = 'info') {
            const statusElement = document.createElement('div');
            statusElement.className = `status ${type}`;
            statusElement.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            statusDiv.appendChild(statusElement);
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }

        function clearStatus() {
            statusDiv.innerHTML = '';
        }

        function testVideoLoad() {
            addStatus('开始测试视频加载...', 'info');
            video.load();
        }

        function testVideoPlay() {
            addStatus('尝试播放视频...', 'info');
            video.play().then(() => {
                addStatus('视频播放成功！', 'success');
            }).catch(error => {
                addStatus(`视频播放失败: ${error.message}`, 'error');
            });
        }

        function testVideoPause() {
            addStatus('暂停视频...', 'info');
            video.pause();
            addStatus('视频已暂停', 'success');
        }

        // 视频事件监听
        video.addEventListener('loadstart', () => {
            addStatus('开始加载视频...', 'info');
        });

        video.addEventListener('loadedmetadata', () => {
            addStatus(`视频元数据加载完成 - 时长: ${Math.round(video.duration)}秒, 尺寸: ${video.videoWidth}x${video.videoHeight}`, 'success');
        });

        video.addEventListener('loadeddata', () => {
            addStatus('视频数据加载完成', 'success');
        });

        video.addEventListener('canplay', () => {
            addStatus('视频可以开始播放', 'success');
        });

        video.addEventListener('canplaythrough', () => {
            addStatus('视频可以流畅播放', 'success');
        });

        video.addEventListener('play', () => {
            addStatus('视频开始播放', 'success');
        });

        video.addEventListener('pause', () => {
            addStatus('视频已暂停', 'info');
        });

        video.addEventListener('ended', () => {
            addStatus('视频播放结束', 'info');
        });

        video.addEventListener('error', (e) => {
            const error = video.error;
            let errorMessage = '未知错误';
            
            if (error) {
                switch (error.code) {
                    case error.MEDIA_ERR_ABORTED:
                        errorMessage = '视频加载被中止';
                        break;
                    case error.MEDIA_ERR_NETWORK:
                        errorMessage = '网络错误';
                        break;
                    case error.MEDIA_ERR_DECODE:
                        errorMessage = '视频解码错误';
                        break;
                    case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                        errorMessage = '视频格式不支持或视频源无效';
                        break;
                }
            }
            
            addStatus(`视频加载错误: ${errorMessage}`, 'error');
        });

        video.addEventListener('waiting', () => {
            addStatus('视频缓冲中...', 'info');
        });

        video.addEventListener('playing', () => {
            addStatus('视频正在播放', 'success');
        });

        // 页面加载完成后自动测试
        window.addEventListener('load', () => {
            addStatus('页面加载完成，开始自动测试...', 'info');
            setTimeout(() => {
                testVideoLoad();
            }, 1000);
        });
    </script>
</body>
</html>
